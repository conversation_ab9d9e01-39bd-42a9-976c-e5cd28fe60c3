<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />

    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>玉玄宫收银系统</title>
    <link rel="stylesheet" href="./css/css-comment.css" />
    <link rel="stylesheet" href="./css/header.css" />
    <link rel="stylesheet" href="./vue/element/<EMAIL>" />
    <link
      rel="stylesheet"
      href="https://at.alicdn.com/t/font_1156348_ycgo943vlrq.css"
    />
    <style>
      .printWrap {
        position: fixed;
        top: 0;
        left: 0;
        width: 180px;
        background: #fff;
      }

      .print-handOver-list,
      .print-sale_list {
        margin-bottom: 10px;
        font-size: 14px;
        zoom: 1;
      }

      .print-sale_list {
        margin-bottom: 0;
      }

      .print-handOver-list:after,
      .print-sale_list:after {
        display: block;
        content: "";
        clear: both;
      }
      .f-main-body-border::after{
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        border: 2px solid #BBBBBB;
        box-sizing: border-box;
        pointer-events: none;
        z-index: 1000;
      }

      /* 全局动画定义 */
      /* .transition {
        transition: all 0.3s ease;
      } */

      /* 为所有交互元素添加过渡效果 */
      .menu_font_nav, .icon_color, .li2, .el-button {
        transition: all 0.3s ease;
      }
    </style>
  </head>
  <body class="f-main-body-border">
    <div id="header" v-cloak>
      <div class="headerBg drag flex justify-between">
        <div class="menu">
          <div class="flex px-4 items-center space-x-2 mr-6 shrink-0 h-full">
            <img
              src="./images/logo.png"
              alt="logo"
              style="width: 76px; height: 21px"
            />
            <div class="bg-black/90 text-white text-xs px-2 py-1 rounded">
              收银系统
            </div>
          </div>
          <ul class="menu_font no-drag relative">
            <template v-for="(value,key) in todos1">
              <template v-if="headerActive == key">
                <el-tooltip class="item" placement="bottom">
                  <div slot="content">
                    <div
                      class="text-sm text-white cursor-pointer"
                      @click.stop="refreshChild(key)"
                    >
                      刷新
                    </div>
                  </div>
                  <li
                    class="menu_font_nav addclass transition z-1"
                    @click.stop="getIndex(key,value)"
                  >
                    {{value.word}}
                  </li>
                </el-tooltip>
              </template>
              <template v-else>
                <li
                  class="menu_font_nav transition z-1 rounded"
                  @click.stop="getIndex(key,value)"
                >
                  {{value.word}}
                </li>
              </template>
            </template>
            <div
              class="f-nav-bg absolute rounded transition-all"
              :style="{left:headerActive*100+'px'}"
            ></div>
          </ul>
          <div class="n" id="audioPart"></div>
        </div>

        <div class="cashier no-drag">
          <!--             <div class="li1">
              <img
                class="li1_img"
                :src="loginInfo.portrait"
                onerror="this.src='images/default.jpg'"
              />
            </div> -->
          <el-dropdown @command="handleCommand" trigger="click">
            <div class="li2 mr-2 transition">
              <span>{{loginInfo.nickname}}</span>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="handOver">交班</el-dropdown-item>
              <el-dropdown-item @click.native="refresh">刷新</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <div class="icons_box">
            <i
              class="iconfont iconzuixiaohua icon_color transition"
              @click="bindMinimize"
            ></i>
            <i
              class="iconfont iconzuidahua1 icon_color transition"
              v-show="isFullScreen"
              @click="bindFullScreen(1)"
            ></i>
            <i
              class="iconfont iconzuidahua icon_color transition"
              v-show="!isFullScreen"
              @click="bindFullScreen(2)"
            ></i>
            <i class="iconfont iconclose icon_color" @click="bindDropOut"></i>
            <!--<a href="header.html" style="color: #fff;">刷新12</a>-->
          </div>
        </div>
      </div>

      <div class="iframe">
        <template v-for="(value,key) in todos1" v-if="inArray(key,pageArr)">
          <iframe
            :key="key"
            :ref="'iframe_'+key"
            class="pageIframe"
            :style="{
              opacity: headerActive==key ? 1 : 0,
              transform: headerActive==key ? 'translateX(0)' : 'translateX(-30px)',
              visibility: headerActive==key ? 'visible' : 'hidden',
              zIndex: headerActive==key ? 2 : 1,
              transition: 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
            }"
            :src="'./' + value.href"
            frameborder="0"
            scrolling="no"
          ></iframe>
        </template>
      </div>

      <!--交班弹框-->
      <el-dialog title="交班信息" :visible.sync="isHandOver" width="45%">
        <ul class="handOver">
          <li class="handOver-list">
            <label class="handOver-lebel">交班人员</label>
            <p class="handOver-info">{{submitWorkList.cashierName}}</p>
          </li>
          <li class="handOver-list">
            <label class="handOver-lebel">交班时间</label>
            <p class="handOver-info">{{submitWorkList.workTime}}</p>
          </li>
          <li class="handOver-list">
            <label class="handOver-lebel">交班店铺</label>
            <p class="handOver-info">{{submitWorkList.storeTag}}</p>
          </li>
        </ul>
        <div class="item_title" style="font-size: 16px; color: #333">
          销售数据
        </div>
        <div class="sale_box">
          <div class="sale_cont">
            <div class="sale_list">
              <div class="sale_name">服务金额</div>
              <div class="sale_price">￥{{submitWorkList.service_money}}</div>
            </div>
            <div class="sale_list">
              <div class="sale_name">产品金额</div>
              <div class="sale_price">￥{{submitWorkList.product_money}}</div>
            </div>
            <div class="sale_list">
              <div class="sale_name">卡项金额</div>
              <div class="sale_price">￥{{submitWorkList.card_money}}</div>
            </div>
            <div class="sale_list">
              <div class="sale_name">直接收款</div>
              <div class="sale_price">￥{{submitWorkList.fast_money}}</div>
            </div>
            <div class="sale_list">
              <div class="sale_name">充值金额</div>
              <div class="sale_price">￥{{submitWorkList.recharge_money}}</div>
            </div>
            <div class="sale_list">
              <div class="sale_name">现金金额</div>
              <div class="sale_price">￥{{submitWorkList.cash_pay}}</div>
            </div>
            <div class="sale_list">
              <div class="sale_name">微信支付</div>
              <div class="sale_price">￥{{submitWorkList.wx_pay}}</div>
            </div>
            <div class="sale_list">
              <div class="sale_name">支付宝金额</div>
              <div class="sale_price">￥{{submitWorkList.ali_pay}}</div>
            </div>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <!--第一版不拥有该功能-->
          <el-button class="bindPrint" @click="printSlip">打印凭条</el-button>
          <el-button type="primary" @click="isSafetyExit = true" @click="shift">
            交班并退出
          </el-button>
        </span>
      </el-dialog>

      <!--退出弹框（直接交班没有其他）-->
      <el-dialog
        title="安全退出"
        :visible.sync="isSafetyExit"
        top="30vh"
        :show-close="false"
        width="400px"
      >
        <p></p>
        <span slot="footer" class="dialog-footer">
          <el-button class="bindPrint CancelOut" @click="isSafetyExit = false">
            取消
          </el-button>
          <el-button plain type="primary" @click="ConfirmExit(0)">
            仅退出
          </el-button>
          <el-button type="primary" @click="ConfirmExit(1)">
            交班并退出
          </el-button>
        </span>
      </el-dialog>

      <div id="prints" class="printWrap" style="display: none">
        <p style="text-align: center">交班信息</p>
        <div class="handOver">
          <div class="print-handOver-list">
            <label class="handOver-lebel" style="float: left">交班人员</label>
            <span class="handOver-info" style="float: right">
              {{submitWorkList.cashierName}}
            </span>
          </div>
          <div class="print-handOver-list">
            <label class="handOver-lebel" style="float: left">交班时间</label>
            <span class="handOver-info" style="float: right">
              {{submitWorkList.workTime}}
            </span>
          </div>
          <div class="print-handOver-list">
            <label class="handOver-lebel" style="float: left">交班店铺</label>
            <span class="handOver-info" style="float: right">
              {{submitWorkList.storeTag}}
            </span>
          </div>
        </div>
        <div
          class="item_title"
          style="
            font-size: 16px;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
          "
        >
          销售数据
        </div>
        <div class="sale_box">
          <div class="">
            <div class="print-sale_list">
              <div class="sale_name" style="float: left">服务金额</div>
              <div class="sale_price" style="float: right">
                ￥{{submitWorkList.service_money}}
              </div>
            </div>
            <div class="print-sale_list">
              <div class="sale_name" style="float: left">产品金额</div>
              <div class="sale_price" style="float: right">
                ￥{{submitWorkList.product_money}}
              </div>
            </div>
            <div class="print-sale_list">
              <div class="sale_name" style="float: left">卡项金额</div>
              <div class="sale_price" style="float: right">
                ￥{{submitWorkList.card_money}}
              </div>
            </div>
            <div class="print-sale_list">
              <div class="sale_name" style="float: left">直接收款</div>
              <div class="sale_price" style="float: right">
                ￥{{submitWorkList.fast_money}}
              </div>
            </div>
            <div class="print-sale_list">
              <div class="sale_name" style="float: left">充值金额</div>
              <div class="sale_price" style="float: right">
                ￥{{submitWorkList.recharge_money}}
              </div>
            </div>
            <div class="print-sale_list">
              <div class="sale_name" style="float: left">现金金额</div>
              <div class="sale_price" style="float: right">
                ￥{{submitWorkList.cash_pay}}
              </div>
            </div>
            <div class="print-sale_list">
              <div class="sale_name" style="float: left">微信支付</div>
              <div class="sale_price" style="float: right">
                ￥{{submitWorkList.wx_pay}}
              </div>
            </div>
            <div class="print-sale_list">
              <div class="sale_name" style="float: left">支付宝金额</div>
              <div class="sale_price" style="float: right">
                ￥{{submitWorkList.ali_pay}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
  <script src="vue/vue2.5.16.js"></script>
  <script src="vue/element/<EMAIL>"></script>
  <script src="js/plugin/jquery-3.2.1.min.js"></script>
  <script src="js/plugin/LodopFuncs.js"></script>
  <script src="js/unocss.theme.js"></script>
  <script src="js/plugin/<EMAIL>"></script>
  <script>
    try {
      var ws;
      var gui = require("nw.gui");
      var win = gui.Window.get();
      var fs = require("fs");
      var domainName = localStorage.getItem("fdb-domainName");
    } catch (e) {

    }
    const baseUrl = domainName + "/index.php?s=";
    var app = new Vue({
      el: "#header",
      data() {
        const test = 1;
        return {
          istbz: false, // false: 打开选择销售功能
          headerActive: 0,
          iframeUrl: test ? "cashier_system.html" : "shouyingtai_kaidan.html",
          todos1: [
            {
              word: "收银台",
              href: test ? "cashier_system.html" : "shouyingtai_kaidan.html",
              id: 0,
            },
            {
              word: "商品",
              href: "shop_index.html",
              id: 1,
            },
            {
              word: "订单",
              href: "order.html",
              id: 2,
            },
            {
              word: "客户",
              href: "huiyuan.html",
              id: 3,
            },
            /*             {
              word: "预约",
              href: "reservation.html",
              id: 4,
            }, */
            /* {
              word: "通知",
              href: "tongzhi.html",
              id: 5,
            }, */
            {
              word: "设置",
              href: "setting.html",
              id: 6,
            },
          ],
          server: "",
          cashier: "",
          loginInfo: {},
          submitWorkList: {},
          isHandOver: false,
          //是否安全退出判断变量
          isSafetyExit: false,
          confirm: 0,
          isFullScreen: false,
          WebSocketPort: "",
          url: baseUrl,
          handOver: {},
          port: 0,
          printSet: {},
          paperwidth: "180",
          pageArr: [0],
          pagesCopy: [],
        };
      },

      mounted: function () {
        this.getLoginInfo();
        this.getWebsocket();
        this.pagesCopy = JSON.parse(JSON.stringify(this.todos1));
      },
      methods: {
        refresh() {
          location.reload();
        },
        // 测试页面切换动画的方法，可以在控制台调用：app.testSwitchAnimation()
        testSwitchAnimation() {
          const pages = Object.keys(this.todos1).map(k => parseInt(k));
          let currentIndex = 0;
          const test = () => {
            this.getIndex(pages[currentIndex]);
            currentIndex = (currentIndex + 1) % pages.length;
            console.log('切换到页面:', pages[currentIndex], '(从左向右切换动画)');
          };
          // 每1.5秒切换一次页面
          return setInterval(test, 1500);
        },
        refreshChild(index, argStr) {
          let src = this.$refs["iframe_" + index][0].src;
          if (argStr) {
            src = src + "?" + argStr;
          }
          $(this.$refs["iframe_" + index])[0].contentWindow.location.replace(
            src
          );
        },
        getIndex: function (index, data) {
          console.log('切换到页面:', index, data ? data.word : '');
          this.headerActive = index;
          const flag = this.pageArr.some((item) => {
            return index === item;
          });
          !flag && this.pageArr.push(index);
        },
        inArray(index, arr) {
          return arr.some((item) => {
            return item === index;
          });
        },
        //判断对象是否为空
        isEmpty: function (obj) {
          for (var key in obj) {
            return false;
          }
          return true;
        },

        //获取登陆信息
        getLoginInfo: function () {
          if (localStorage.getItem("loginInfo")) {
            this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
            this.server = fs.readFileSync("./views/url.txt").toString();
            return false;
          } else {
            nw.Window.open(
              "public/Login.html",
              {
                width: 768,
                height: 470,
                kiosk: true,
                frame: false,
              },
              function (win) {
                window.close();
                win.on("closed", function () {
                  win = null;
                });
              }
            );
          }
        },

        //获取交班信息
        getSubmitWork: function () {
          var _self = this;
          $.ajax({
            url: _self.url + "/android/login/submitWork",
            type: "post",
            data: {
              cashier_id: _self.loginInfo.id, //收银员id
              confirm: _self.confirm, //确定交班返回1 ,查看数据返回0/不返
              merchantid: _self.loginInfo.merchantid, //商户id
              shift_no: _self.loginInfo.shift_no, //加班班次
              storeid: _self.loginInfo.storeid, //商铺id
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1 && !_self.isEmpty(res.data)) {
                if (isNaN(res.data.orther_pay) == true) {
                  res.data.orther_pay = "0.00";
                }
                _self.submitWorkList = res.data;
              }
            },
          });
        },

        handleCommand: function (command) {
          switch (command) {
            case "handOver":
              this.getSubmitWork();
              this.isHandOver = true;
              break;
          }
        },

        //打印凭条点击事件
        printSlip: function () {

          Preview1();
          this.isHandOver = false;
        },

        // // 没有安装打印机
        // noPrint: function () {
        //     let self = this;
        //     self.$message({
        //         type: "error",
        //         message: '打印机未准备好,无法打印',
        //         duration: 1500,
        //         onClose: function () {
        //             LODOPbol = false;
        //         }
        //     })
        // },

        //最小化
        bindMinimize: function () {
          win.minimize();
        },

        //全屏展示
        bindFullScreen: function (data) {
          switch (data) {
            case 1:
              this.isFullScreen = false;
              win.leaveFullscreen();
              // win.width = 1200;
              // win.height = 750;
              // win.setMinimumSize(1200, 750);
              // win.x = 0;
              // win.y = 0;
              break;
            case 2:
              this.isFullScreen = true;
              win.enterFullscreen();
              break;
          }
        },

        //退出
        bindDropOut: function () {
          var _self = this;
          _self.isSafetyExit = true;
        },

        //头部X号确认安全退出（确认交班）
        ConfirmExit: function (num) {
          if (num == 0) {
            return win.close();
          }
          var _self = this;
          $.ajax({
            url: _self.url + "/android/login/submitWork",
            type: "post",
            data: {
              cashier_id: _self.loginInfo.id, //收银员id
              confirm: 1, //确定传1 ,查看数据传0/不传
              merchantid: _self.loginInfo.merchantid, //商户id
              shift_no: _self.loginInfo.shift_no, //加班班次
              storeid: _self.loginInfo.storeid, //商铺id
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1 && !_self.isEmpty(res.data)) {
                _self.submitWorkList = res.data;
              } else {
                _self.telephone = self.password = self.errTip = "";
                localStorage.removeItem("loginInfo", JSON.stringify(res.data));
                win.close();
              }
              // console.log('响应的交班数据', _self.submitWorkList);
            },
          });
        },

        //交班并退出
        shift: function () {
          var _self = this;
          _self.isSafetyExit = true;
        },

        /*  语音播报*/
        //websocket接口
        getWebsocket: function () {
          var _self = this;
          $.ajax({
            url: _self.url + "/android/Websocket/Websocket",
            type: "post",
            success: function (res) {
              // // var res = JSON.parse(res);
              if (res.code == 1 && !_self.isEmpty(res.data)) {
                if (res.data.status == 1) {
                  _self.port = res.data.port;
                  _self.sendsocket();
                }
              } else if (res.code == 0) {
                _self.port = 0;
              }
            },
          });
        },

        //
        onmessage: function (evt) {
          var self = this;
          var data = JSON.parse(evt.data);
          // console.log(data.type);
          if (data.type == "say") {
            // console.log(data.content);
            if (data.content instanceof Object) {
              if (data.content.type == 2) {
                //self.newOrder(data.content);
                self.setSpeech(data.content);
              }
            }
          }
        },

        //
        sendsocket: function () {
          var port = this.port;
          // 创建websocket

          var id = this.loginInfo.storeid;
          ws = new WebSocket("ws://" + this.server + ":" + port);
          // ws.onopen = onopen;
          // 当有消息时根据消息类型显示不同信息
          //连接状态
          ws.onopen = function () {
            var data = { type: "login", uid: "store" + id, type_id: 1 };
            var jsonData = JSON.stringify(data);
            ws.send(jsonData);
          };

          ws.onmessage = this.onmessage;

          //使用工单id登录
          ws.onclose = function () {
            //alert("连接关闭，定时重连");
            //connect();
          };
          ws.onerror = function () {
            // alert("连接错误,请检查");
          };
        },

        // 语音合成
        setSpeech: function (data) {
          // console.log(data);
          var _self = this;
          $.ajax({
            // TODO 语音地址
            url: _self.url + "/speech",
            type: "post",
            data: {
              text: data.text,
              spd: "6",
              pit: "3",
              vol: "8",
              per: "0",
            },
            success: function (res) {
              if (res.ret == 0) {
                var audios = document.getElementById("audioPart");
                audios.innerHTML = `
                            <audio id="audio" controls="controls" style="display:none">
                                <source src="./audio/tts.audio.mp3" type="audio/mpeg">
                            </audio>
                            `;
                var audio = document.getElementById("audio");
                audio.play();
                audio.addEventListener(
                  "ended",
                  function () {
                    $.ajax({
                      // TODO 语音地址
                      url: _self.url + "/del",
                      type: "get",
                      success: function (res) {

                        audios.innerHTML = "";
                      },
                    });
                  },
                  false
                );
                _self.newOrder(data);
              }
            },
          });
        },

        newOrder: function (data) {
          const h = this.$createElement;
          this.$notify({
            title: data.title,
            message: h(
              "i",
              {
                style: "color: teal",
              },
              data.text
            ),
          });
        },
        // 跳转到对应页面  top.app.toPage(href); href= 页面路径 + 参数 （参数可以没有）
        toPage(href) {
          let arr = href.split("?");
          let index = -1;
          this.pagesCopy.some((item, i) => {
            if (arr[0] == item["href"]) {
              index = i;
              return true;
            }
            return false;
          });
          if (index === -1) {
            return this.$message.error("没有该页面");
          }

          this.getIndex(index);
          let argStr = "";
          if (arr.length > 1) {
            argStr = arr[1];
          }

          this.$nextTick(() => {
            this.refreshChild(index, argStr);
          });
        },
      },

      watch: {
        todos1: {
          deep: true,
          handler(n) {
            console.error(n);
          },
        },
      },
    });

    function parentMethod() {
      app.headerActive = 0;
    }

    function activeHeader(index) {
      app.headerActive = index;
    }



    // window.onload = function () {
    //     console.log(app.port);
    //     if (app.port != 0) {
    //         var timer = setInterval(function () {
    //             app.sendsocket();
    //             // clearInterval(timer)
    //         }, 36000)
    //     }
    // }

    var LODOP; //声明为全局变量
    var LODOPbol = true;

    function Preview1() {
      if (!LODOPbol) {
        return;
      }
      LODOP = getLodop();
      //console.log(LODOP);
      if (LODOP) {
        LODOP.SET_PREVIEW_WINDOW(0, 1, 0, 800, 600, "");
        LODOP.SET_SHOW_MODE("PREVIEW_NO_MINIMIZE", true); //预览窗口禁止最小化，并始终最前
        LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREWINDOW", 1);
        AddPrintContent();
        LODOP.PRINT();
      } else {
        app.$message({
          type: "error",
          message: "打印机未准备好,无法打印",
          duration: 1500,
          onClose: function () {
            LODOPbol = false;
          },
        });
      }
    }

    function AddPrintContent(strCode, strName) {
      var strStyleCSS = "",
        styles = document.querySelectorAll("style,link");
      for (var i = 0; i < styles.length; i++) {
        strStyleCSS += styles[i].outerHTML;
      }
      var str = document.getElementById("prints").innerHTML;
      LODOP.SET_PREVIEW_WINDOW(1, 0, 0, 0, 0, "");
      LODOP.SET_PRINT_MODE("PRINT_PAGE_PERCENT", "100%");
      LODOP.ADD_PRINT_HTM(
        0,
        0,
        "100%",
        "100%",
        strStyleCSS + "<body leftmargin=0 topmargin=0>" + str + "</body>"
      );
      // LODOP.ADD_PRINT_HTM(0, 0, "100%", "100%", str);
      LODOP.SET_PRINT_PAGESIZE(3, 5700, 50, "");
      // LODOP.ADD_PRINT_HTM(0, 0, "100%", "100%", document.getElementById("prints").innerHTML);
      // LODOP.SET_PRINT_PAGESIZE(3, 5700, 50, "");
    }

    // function initSocket() {
    //     if (!"WebSocket" in window) {
    //         console.log("您的浏览器不支持 WebSocket!");
    //         return;
    //     }
    //     webSocket = new WebSocket("ws://" + document.domain + ":8283");
    //     webSocket.onopen = handleSend;
    //     webSocket.onmessage = handleMessage;
    //     webSocket.onclose = handleClose;
    //     webSocket.onerror = handleError;
    // }
    //
    // // 向服务器端发送数据
    // function handleSend() {
    //     // Web Socket 已连接上，使用 send() 方法发送数据
    //     testing();
    // }
    //
    // // 处理服务器端发送过来的数据
    // function handleMessage(evt) {
    //     var received_msg = evt.data;
    //     console.log(received_msg);
    //     var obj = JSON.parse(received_msg);
    //     if (obj.type == 'say') {
    //         console.log(obj.content);
    //         if (obj.content instanceof Object) {
    //             if (obj.content.type == 2) {
    //                 var str = obj.content.text + '<br/>' + '时间：' + obj.content.time;
    //                 var title = obj.content.title ? obj.content.title : '通知';
    //                 vueCommon.showNotification(title, str)
    //             }
    //         }
    //     }
    // }
    //
    // // 处理连接关闭事件
    // function handleClose() {
    //     console.log("连接已关闭...");
    // }
    //
    // // 处理WebSocket错误
    // function handleError() {
    //     console.log("WebSocketError!");
    // }
    //
    // function testing() {
    //     var data = {"type": "login", "uid": "store37", "type_id": 1};
    //     var jsonData = JSON.stringify(data);
    //     webSocket.send(jsonData);
    // }
  </script>
</html>

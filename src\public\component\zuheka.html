<transition :name="transitionDirection" mode="out-in">
  <ul class="main-right">
    <li style="display: flex; width: 100%">
      <!--选择订单内容-->
      <div class="server">
        <!--产品和服务-->
        <!-- <div class="server_chioce">服务</div> -->
        <!--<div class="server_line1"></div>-->
        <!--服务和搜索-->
        <div class="search_menu">
          <div class="search_bor">
            <div class="el-input el-input--suffix">
              <input
                type="text"
                autocomplete="off"
                placeholder="请输入服务名称"
                name="search_keyword"
                class="el-input__inner"
                v-model.trim="search_keyword"
                ref="search_keyword"
                @keyup.enter.exact.stop="billingInquiryEnter"
              />
              <span class="el-input__suffix" @click="billingInquiryEnter">
                <span class="el-input__suffix-inner">
                  <i class="el-input__icon el-icon-search"></i>
                </span>
              </span>
            </div>
          </div>
          <!--选择标签-->
          <!--     <div class="chooseLabelWrap">
            <ul
              class="chooseLabel"
              style="margin-right: 15px"
              ref="comChooseTag"
            >
              <li
                v-for="(value,key) in cashier_open_order_service_label"
                ref="comChooseLiTag"
                @click="bind_choice_server(value,key)"
              >
                <p
                  :class="isActiveServer == key ? 'bg_label' : '' "
                  class="open_order_search"
                >
                  {{value.label_name}}
                </p>
              </li>
            </ul>
          </div> -->
          <!--可选服务列表 uuai-->
          <div
            class="search_detail serverWrap"
            ref="serverCardWrap"
            style="height: calc(100vh - 72px)"
          >
            <div
              v-infinite-scroll="loadMoreProduct"
              infinite-scroll-disabled="isServerScroll"
              infinite-scroll-distance="10"
              infinite-scroll-immediate-check="isServerScroll"
            >
              <div
                class="search_detail1"
                v-for="(value,index) in zhk_server_name"
                @click="bind_zhk_add_server(value,index)"
              >
                <div class="serach_detail_info">
                  <img
                    class="serach_detail_img"
                    :src="value.imgurl"
                    onerror="this.src='images/default.jpg'"
                  />
                </div>
                <div class="service_name-price">
                  <p class="serach_detail_info_font1 service_name">
                    {{value.service_name}}
                  </p>
                  <p class="serach_detail_info_font2">￥{{value.price}}</p>
                </div>
              </div>
              <!-- 触底效果 -->
              <div v-if="busy" class="loadingtip">{{loadingtip}}</div>
            </div>
          </div>
        </div>
      </div>
      <!--开单内容-->
      <div class="open_details">
        <!--开单详情标题-->
        <div class="open_details_border">
          <div class="open_details_title">充卡详情</div>
          <div class="open_details_title_font2" @click="over_open">
            清空页面
          </div>
        </div>
        <!--搜索夹内容和支付-->
        <div
          class="main-body"
          style="
            height: calc(100vh - 60px);
            display: flex;
            justify-content: space-between;
            flex-direction: column;
          "
        >
          <div
            class="open_details_info"
            style="flex: 1; height: 0; display: flex; flex-direction: column"
          >
            <!--搜索-->
            <div>
              <div class="zhk_presonal_info" v-if="is_sechargeMember">
                <div class="presonal_touxiang">
                  <i
                    class="iconfont icontouxiang"
                    style="font-size: 80px; color: #ccc"
                  ></i>
                  <!--<img :src="vipHeadPortrait" alt="" class="zhkVipHead">-->
                </div>
                <div class="presonal_data">
                  <div class="presonal_tel">
                    <input
                      type="text"
                      placeholder="输入手机号或刷实体卡"
                      class="tel_input"
                      v-model.trim="zhk_memberObj.phone"
                      ref="memberPhone"
                      @input="bindzhkInquireMember(zhk_memberObj.phone)"
                      @keyup.enter="bindInquire(zhk_memberObj.phone)"
                    />
                  </div>
                  <div class="presonal_name">
                    <input
                      type="text"
                      placeholder="姓名"
                      class="name_input"
                      v-model.trim="MemberName"
                    />
                    <div style="display: flex">
                      <div
                        :class="zhk_sex == 2 ? 'vip_sex_show':'vip_sex_none'"
                        class="iconfont iconnv"
                        style="visibility: hidden"
                      >
                        &emsp;女
                        <!--@click="chioce_sex(1)">&emsp;女-->
                      </div>
                      <div
                        :class="zhk_sex == 1 ? 'vip_sex_show':'vip_sex_none'"
                        class="iconfont iconnan"
                        style="margin-left: 10px; visibility: hidden"
                      >
                        &emsp;男
                        <!--@click="chioce_sex(2)">&emsp;男-->
                      </div>
                    </div>
                  </div>
                </div>
                <div class="mt-2">
                  <el-button
                    class="h-fit"
                    type="primary"
                    @click="isShowMemberSearch = true"
                  >
                    客户列表
                  </el-button>
                </div>
              </div>
              <div
                class="zhk_presonal_info"
                style="padding: 15px"
                v-if="is_showgeMember"
              >
                <div
                  v-if="cz_huiyuanxinxi && cz_huiyuanxinxi.id"
                  class="flex items-center w-full"
                >
                  <div class="member-pic">
                    <!-- <i class="iconfont icontouxiang" style="font-size: 80px;color: #ccc;"></i> -->
                    <img
                      :src="cz_huiyuanxinxi.pic || 'images/touxoang.png'"
                      alt=""
                      onerror="this.src='images/touxoang.png'"
                    />
                    <!--<img src="./images/default.jpg" alt="">-->
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center space-x-2">
                      <div class="text-xl font-bold">
                        {{cz_huiyuanxinxi.member_name}}
                      </div>
                      <div class="text-gray">
                        {{cz_huiyuanxinxi.remarks_name}}
                      </div>

                      <el-tag
                        size="mini"
                        v-if="cz_huiyuanxinxi.sex == 1 || cz_huiyuanxinxi.sex == 2"
                        :type="cz_huiyuanxinxi.sex == 1 ? 'primary' : cz_huiyuanxinxi.sex == 2 ? 'danger' : 'info'"
                      >
                        {{cz_huiyuanxinxi.sex == 1 ? '男' :cz_huiyuanxinxi.sex
                        == 2 ? '女' : ''}}
                      </el-tag>
                      <el-tag
                        v-if="cz_huiyuanxinxi.is_vip"
                        size="mini"
                        type="warning"
                      >
                        会员
                      </el-tag>
                    </div>
                    <div class="flex text-sm mt-2 space-x-10">
                      <div>
                        <div>
                          <span class="text-gray">账号/手机：</span>
                          {{memberInfo.phone}}
                        </div>
                        <div>
                          <span class="text-gray">会员编号：</span>
                          {{memberInfo.member_number}}
                        </div>
                      </div>
                      <div>
                        <div>
                          <span class="text-gray">累计消费：</span>
                          ￥{{memberInfo.total | filterMoney}}
                        </div>
                        <div>
                          <span class="text-gray">消费次数：</span>
                          {{memberInfo.count}}
                        </div>
                      </div>
                      <div>
                        <div>
                          <span class="text-gray">剩余卡次：</span>
                          {{memberInfo.MaxNum}}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="member-del shrink-0">
                    <i
                      class="el-icon-delete cursor-pointer"
                      @click.stop="bindDelMemberInfo"
                    ></i>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="open_server_name"
              style="flex: 1; height: auto; overflow-y: auto"
            >
              <div class="pb-4 space-y-2">
                <div
                  class="px-8 py-4 border-1 border-solid border-gray-200 rounded-md flex items-center justify-between transition hover:shadow-lg"
                  v-for="(value,index) in zhk_server_details_name"
                >
                  <div class="flex-1 shrink-0">
                    <div class="text-lg font-bold">
                      {{value.service_name| ellipsis}}
                    </div>
                    <div class="text-gray mt-1">￥{{value.zhk_price_show}}</div>
                  </div>
                  <div class="o-numberInput-box mr-4" style="width: 130px">
                    <i class="el-icon-minus" @click="jianshao(index)"></i>
                    <el-input
                      v-model="value.numberAvailable"
                      size="small"
                      style="width: 80px"
                      max="9999"
                      step="1"
                      maxlength="4"
                      @change="handleNumInputChange(index)"
                    ></el-input>
                    <i class="el-icon-plus" @click="zengjia(index)"></i>
                  </div>
                  <div class="flex-1">
                    ￥{{(value.totalAmount/100).toFixed(2)}}
                  </div>
                  <div
                    class="el-icon-delete cursor-pointer text-gray hover:text-primary"
                    @click="zhk_open_details_price_del(index)"
                  ></div>
                </div>
              </div>
            </div>
          </div>
          <!--备注和支付-->
          <div class="open_details_pay" style="background: #f1f1f1">
            <div class="flex items-center px-4" style="height: 58px">
              <div class="text-regular text-sm px-4 shrink-0">订单备注</div>
              <el-input
                type="text"
                placeholder="请输入"
                v-model="beizhu_info"
              ></el-input>
            </div>
            <div class="zhk_chioce_Discount pt-4">
              <el-form
                :model="cardinfo"
                size="small"
                label-width="100px"
                class="demo-ruleForm"
              >
                <el-form-item label="有效期">
                  <el-radio v-model="cardinfo.permanent" label="1">
                    永久有效
                  </el-radio>
                  <el-radio v-model="cardinfo.permanent" label="2">
                    限制使用天数
                  </el-radio>
                  <el-radio v-model="cardinfo.permanent" label="3">
                    固定使用日期
                  </el-radio>
                </el-form-item>
                <template v-if="cardinfo.permanent==2">
                  <el-form-item label="有效天数" prop="validity_time">
                    <el-input
                      v-model="cardinfo.validity_time"
                      placeholder="请输入天数"
                      style="width: 180px"
                    >
                      <template slot="append">天</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="生效方式" prop="effectiveMethod">
                    <el-radio v-model="cardinfo.effectiveMethod" label="1">
                      购买后立刻生效
                    </el-radio>
                    <el-radio v-model="cardinfo.effectiveMethod" label="2">
                      使用一次后生效
                    </el-radio>
                  </el-form-item>
                </template>
                <el-form-item
                  v-if="cardinfo.permanent==3"
                  label="到期日期"
                  prop="zuheka_validity"
                >
                  <el-date-picker
                    v-model="cardinfo.zuheka_validity"
                    type="date"
                    value-format="yyyy-MM-dd"
                    :picker-options="giftPickTimeOptions"
                    placeholder="选择日期"
                  ></el-date-picker>
                </el-form-item>
              </el-form>
              <!-- <div class="zhk_chioce_Discount_font1" style="flex: 1">
                  <span class="chioce_Discount_font0 pr-2">
                    <span class="text-red">*</span>
                    有效期至
                  </span>
                  <span class="chioce_Discount_font2">
                    <el-date-picker
                      v-model="zuheka_validity"
                      type="date"
                      placeholder="请选择"
                      value-format="yyyy-MM-dd"
                      :picker-options="giftPickTimeOptions"
                    ></el-date-picker>
                  </span>
                </div>
                <div class="chioce_Discount_font1_check" style="flex: 1">
                  <el-checkbox v-model="checked">永久有效</el-checkbox>
                </div> -->
            </div>
            <div class="zhk_chioce_Discount1">
              <el-radio v-model="discountMethods" label="deduction">
                优惠金额&emsp;
                <input
                  type="text"
                  placeholder="请输入金额"
                  v-model="deductionPrice"
                  class="zhk_Discount_font1_input1"
                  @click="handleDiscountMethodsClick('deduction')"
                />
              </el-radio>
              <el-radio v-model="discountMethods" label="discount">
                折扣(%)&emsp;
                <input
                  type="text"
                  placeholder="请输入折扣"
                  v-model="input_dis"
                  class="zhk_Discount_font1_input2"
                  @click="handleDiscountMethodsClick('discount')"
                />
              </el-radio>
              <!-- <div class="chioce_Discount_font1" style="flex: 1">
                  <span class="chioce_Discount_font0">优惠金额&emsp;</span>
                  <input
                    type="text"
                    placeholder="请输入金额"
                    v-model="deductionPrice"
                    class="zhk_Discount_font1_input1"
                  />
                </div>
                <div class="chioce_Discount_font1" style="flex: 1">
                  <span class="chioce_Discount_font0">折扣（%）&emsp;</span>
                  <input
                    type="text"
                    placeholder="请输入折扣"
                    v-model="input_dis"
                    class="zhk_Discount_font1_input2"
                  />
                </div> -->
            </div>
            <div class="zhk_chioce_Discount2" v-if="!istbz">
              <div class="chioce_Discount_font1">
                <span class="text-regular text-sm" @click="zhkchange_xiaoshou">
                  选择销售&emsp;
                </span>
                <!--<span class="chioce_Discount_font2" @click="zhkchange_xiaoshou" v-if="SalesShow">请选择</span>-->
                <!--<span class="chioce_Discount_font2" @click="zhkchange_xiaoshou" v-if="SalesShow">{{SalesShow}}</span>-->
                <span class="chioce_Discount_font2" @click="zhkchange_xiaoshou">
                  {{SalesShow || selesShows}}
                </span>
              </div>
              <!-- <div class="chioce_Discount_font3">
                  <i class="iconfont iconarrow"></i>
                </div> -->
            </div>
            <div
              class="zhk_chioce_Discount2 chioce_Discount"
              v-if="(istbz && (cz_huiyuanxinxi && cz_huiyuanxinxi.bindStaffInfo && cz_huiyuanxinxi.bindStaffInfo.id))"
              style="height: auto; min-height: 58px; border: none"
            >
              <div class="chioce_Discount_font1">
                <span class="chioce_Discount_font0">销售&emsp;</span>
                <template>
                  <span class="chioce_Discount_font2">
                    {{cz_huiyuanxinxi.bindStaffInfo.nickname}}({{cz_huiyuanxinxi.bindStaffInfo.job_num}})
                  </span>
                </template>
              </div>
              <div
                class="chioce_Discount_font1"
                style="height: auto; min-height: 34px"
              >
                <span class="chioce_Discount_font0">协助&emsp;</span>
                <span
                  class="chioce_Discount_font2"
                  @click="chooseHelpStaff(cz_huiyuanxinxi.bindStaffInfo.id)"
                >
                  <template
                    v-if="(helpStaffArr[isactive1] && helpStaffArr[isactive1].length>0)"
                  >
                    <template v-for="helpStaff in helpStaffArr[isactive1]">
                      <el-tag style="margin: 4px" size="mini">
                        {{helpStaff.nickname}}({{helpStaff.job_num}})
                      </el-tag>
                    </template>
                  </template>
                  <template v-else>未选择协助销售</template>
                </span>
              </div>
            </div>
            <div class="open_details_pay_choice">
              <div class="open_details_pay_choice_font1">
                <span>待支付&nbsp;&nbsp;</span>
                <span>￥{{pay_all_show || 0}}</span>
              </div>
              <div class="order_three">
                <div
                  class="open_details_pay_choice_font4"
                  @click="zhkcollection('receipt')"
                >
                  收款
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </li>
    <!--选择销售模态框-->
    <el-dialog
      title="选择协助销售"
      :visible.sync="helpStaffVisible"
      width="35%"
      top="7vh"
    >
      <div style="height: calc(100vh - 500px); overflow: auto">
        <el-checkbox-group v-model="checkHelpStaffArr">
          <template v-for="(helpStaff,index) in helpStaffAll">
            <div class="xuazne_xiaoshou" v-if="bindStaffId!=helpStaff.id">
              <el-checkbox :label="helpStaff" style="height: 25px; width: 25px">
                {{helpStaff.nickname}} ({{helpStaff.job_num}})
              </el-checkbox>
            </div>
          </template>
        </el-checkbox-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="closeHelpStaffVisible(0)">
          取消
        </el-button>
        <el-button type="primary" @click="closeHelpStaffVisible(1)">
          确定
        </el-button>
      </span>
    </el-dialog>

    <!--选择销售模态框-->
    <el-dialog
      title="选择销售"
      :visible.sync="zhk_xiao_shou"
      width="35%"
      top="7vh"
    >
      <!--<div class="xuanze_jishi_search">-->
      <!--<el-input placeholder="输入销售名称" suffix-icon="el-icon-search"></el-input>-->
      <!--</div>-->
      <div style="height: calc(100vh - 500px); overflow: auto">
        <div class="xuazne_xiaoshou" v-for="(value , index) in zhkxiaoshous ">
          <el-checkbox
            v-model="value.is_choice_xiaoshou"
            style="height: 25px; width: 25px"
            @change="chioce_xiaoshou(index,value.is_choice_xiaoshou,value.id)"
          >
            {{value.nickname}}
          </el-checkbox>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="zhk_xiaoshou_over">
          取消
        </el-button>
        <el-button type="primary" @click="zhk_xiaoshou_save">确定</el-button>
      </span>
    </el-dialog>
    <!-- 点击充值收款后出现开单框 -->

    <template v-if="buy_receipt">
      <app-pay
        :buy-receipt="buy_receipt"
        :login-info="loginInfo"
        :use-card="isRechargeCard"
        :order-no="orderNo"
        :bill-to-pay="billToPay"
        :is-pay-status="isPayStatus"
        @close-pay="bindClosePay"
      ></app-pay>
    </template>

    <!-- 客户列表框 -->
    <member-search-dialog
      :value="isShowMemberSearch"
      :login-info="loginInfo"
      @sync-is-show-memeber-search="isShowMemberSearch = $event"
      @handle-select-member="handleSelect"
    ></member-search-dialog>
  </ul>
</transition>
